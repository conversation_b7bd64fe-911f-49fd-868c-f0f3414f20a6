import { useEffect, useMemo, useRef, useState } from 'react';
import { Divider, Drawer, Tag, Timeline } from 'antd';
import classNames from 'classnames';
import dayjs from 'dayjs';
import { isNil } from 'lodash';
import MapRouteLine from './RouteLine';
import auditIcon from '@/assets/images/audited.svg';
import locationIcon from '@/assets/images/location.svg';
import midIcon from '@/assets/images/mid.svg';
import notAuditIcon from '@/assets/images/not-audited.svg';

export interface AuditRoute {
  /**
   * 批次ID
   */
  batchId?: null | string;
  /**
   * 批次开始时间
   */
  beginTime?: string;
  /**
   * 批次结束时间
   */
  endTime?: string;
  /**
   * 预估执行时长，单位分钟
   */
  estimateExecuteTime?: number | null;
  /**
   * 线路ID
   */
  routeId?: number | null;
  /**
   * 门店信息列表
   */
  shopInfoDTOS?: FoodSafetyNormalRoutShopInfoDTO[] | null;
}

export interface FoodSafetyNormalRoutShopInfoDTO {
  /**
   * 稽核时间 单位：分钟
   */
  auditTime?: number | null;
  /**
   * 纬度
   */
  latitude?: null | number;
  /**
   * 经度
   */
  longitude?: null | number;
  /**
   * 门店地址
   */
  shopAddress?: null | string;
  /**
   * 门店ID
   */
  shopId?: null | string;
  /**
   * 门店名称
   */
  shopName?: null | string;
  /**
   * 排序
   */
  sort?: number | null;
  /**
   * 任务ID
   */
  taskId?: number | null;
  /**
   * 任务状态
   */
  taskStatus?: any;
  /**
   * 到达该门店时间 单位：分钟
   */
  travelTime?: number | null;
}

interface MapRouteProps {
  open: boolean;
  onClose: () => void;
  data: AuditRoute;
}

export default function MapRoute({ open, onClose, data }: MapRouteProps) {
  const [activeIndex, setActiveIndex] = useState<number | null>(null);
  const mapInstance = useRef<MapRouteLine | null>();

  /**
   * 定位
   * 分为：起点、途径点、终点
   *  [116.379028, 39.865042]
   *  [116.427281, 39.903719]
   *  [[116.397455, 39.909187]]
   */
  const position = useMemo(() => {
    const start = [data?.shopInfoDTOS[0].longitude, data?.shopInfoDTOS[0].latitude];
    const end = [
      data?.shopInfoDTOS[data?.shopInfoDTOS.length - 1].longitude,
      data?.shopInfoDTOS[data?.shopInfoDTOS.length - 1].latitude,
    ];
    const waypoints = data?.shopInfoDTOS
      .slice(1, data?.shopInfoDTOS.length - 1)
      .map((item) => [item.longitude, item.latitude]);

    return {
      start,
      end,
      waypoints,
    };
  }, [data]);

  /**
   * 已查门店
   */
  const auditShopCount = useMemo(() => {
    return data?.shopInfoDTOS?.filter((item) => ['COMPLETED', 'AUDITING'].includes(item.taskStatus))?.length;
  }, [data]);

  /**
   * 全部门店
   */
  const allShopCount = useMemo(() => {
    return data?.shopInfoDTOS?.length;
  }, [data]);

  useEffect(() => {
    const initializeMap = async () => {
      try {
        mapInstance.current = new MapRouteLine(data?.shopInfoDTOS);

        await mapInstance.current.initMap('map_container_unique');

        // 现在可以安全地调用路径规划了
        await mapInstance.current.routePlanning(position.start, position.end, position.waypoints);
      } catch (error) {
        console.error('地图初始化失败:', error);
      }
    };

    if (open && data?.shopInfoDTOS?.length) {
      // 初始化地图
      initializeMap();
    }

    // 组件卸载时清理
    return () => {
      if (mapInstance.current) {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        mapInstance?.current?.destroy();
        setActiveIndex(null);
      }
    };
  }, [open, position.end, position.start, position.waypoints, data?.shopInfoDTOS]);

  const timelineItems = useMemo(() => {
    return data?.shopInfoDTOS.map((item, index) => ({
      dot: (
        <div
          className={classNames(
            'w-6 h-6 bg-[#23C343] rounded-full text-white flex items-center justify-center text-base font-medium',
            {
              'bg-[#C9CDD4]': ['COMPLETED', 'AUDITING'].includes(item.taskStatus),
            },
          )}
        >
          {index + 1}
        </div>
      ),
      children: (
        <div
          style={{
            borderBottom: '1px solid rgba(0, 0, 0, 0.06)',
          }}
          className="flex flex-col pb-4"
          onClick={() => {
            setActiveIndex(index);
            mapInstance.current.getMap().setCenter([item.longitude, item.latitude]);
            // 放大
            mapInstance.current.getMap().setZoom(15);
          }}
        >
          <div className="flex flex-row items-center justify-between">
            <div className="flex flex-row items-center">
              {index === 0 && (
                <Tag bordered color="success">
                  起点
                </Tag>
              )}
              {index === data?.shopInfoDTOS.length - 1 && (
                <Tag bordered color="error">
                  终点
                </Tag>
              )}
              <div className="text-sm font-medium text-[#1D2129]">{item.shopName}</div>
            </div>
            <img src={locationIcon} alt="location" className="w-5 h-5" />
          </div>
          {item.shopAddress && <div className="text-xs font-normal text-[#86909C] mt-1">{item.shopAddress}</div>}
        </div>
      ),
    }));
  }, [data?.shopInfoDTOS]);

  const days = useMemo(() => {
    const minutes = data?.estimateExecuteTime || 0;
    const day = Math.floor(minutes / 60 / 24);
    const hours = Math.floor(minutes / 60) % 24;

    return `${day}天${hours}分钟`;
  }, [data?.estimateExecuteTime]);

  return (
    <Drawer width={640} title="路径规划" open={open} onClose={onClose}>
      <div className="bg-[#F7F8FA] p-3 rounded-lg">
        <div className="text-[#A7302A] text-bas font-medium">
          已查门店：{auditShopCount}/{allShopCount}
        </div>
        <div className="flex flex-row items-center mt-1">
          <div className="text-[#4E5969] text-sm font-normal">途经点：{allShopCount - 2}个门店</div>
          <Divider type="vertical" />
          <div className="text-[#4E5969] text-sm font-normal">总行程：{mapInstance.current?.getDistance()}公里</div>
        </div>
        <div className="text-sm font-normal text-[#4E5969]">
          执行时间：{dayjs(data?.beginTime).format('YYYY-MM-DD HH:mm')} -{' '}
          {dayjs(data?.endTime).format('YYYY-MM-DD HH:mm')}（预估共{days}）
        </div>
      </div>
      <Timeline mode="left" className="mt-6" items={timelineItems} />
      <div style={{ height: '460px' }} className="relative">
        <div id="map_container_unique" style={{ height: '460px' }} />
        <div className=" bg-[#1D2129] opacity-70 z-[999] absolute bottom-[8px] left-[8px] p-2 rounded flex flex-row">
          <div className="flex flex-col">
            <span className="text-white font-medium text-xs">门店类型说明</span>
            <div className="flex flex-row items-center mt-2">
              <img src={notAuditIcon} alt="location" className="w-5 h-5" />
              <span className="text-white font-medium text-xs ml-2">未稽核门店</span>
            </div>
            <div className="flex flex-row items-center mt-2">
              <img src={auditIcon} alt="location" className="w-5 h-5" />
              <span className="text-white font-medium text-xs ml-2">已稽核门店</span>
            </div>
            <div className="flex flex-row items-center mt-2">
              <img src={midIcon} alt="location" className="w-5 h-5" />
              <span className="text-white font-medium text-xs ml-2">新增门店</span>
            </div>
          </div>
          {!isNil(activeIndex) && (
            <>
              <div className="w-[1px] bg-white opacity-10 ml-3 mr-2" />
              <div className="flex flex-col justify-center">
                <div className="text-white font-medium text-xs">门店编号：{data?.shopInfoDTOS[activeIndex].shopId}</div>
                <div className="text-white font-medium text-xs mt-2">
                  门店名称：{data?.shopInfoDTOS[activeIndex].shopName}
                </div>
                <div className="text-white font-medium text-xs mt-2">
                  路线编号：{data?.shopInfoDTOS[activeIndex].sort}
                </div>
                <div className="text-white font-medium text-xs mt-2">
                  坐标：{data?.shopInfoDTOS[activeIndex].longitude},{data?.shopInfoDTOS[activeIndex].latitude}
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </Drawer>
  );
}
